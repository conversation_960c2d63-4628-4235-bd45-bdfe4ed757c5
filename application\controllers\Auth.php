<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends CI_Controller{
    
    function __construct() {
        parent::__construct();
        $this->load->model('model_operator');
    }

    public function login(){
        if(isset($_POST['submit'])){
            // proses login disini
            $username   =   $this->input->post('username');
            $password   =   $this->input->post('password');
            if (empty($username) || empty($password)) {
                $this->session->set_flashdata('error', 'Username and password are required.');
                redirect('Auth/login');
                return;
            }
            if (strlen($password) < 4) {
                $this->session->set_flashdata('error', 'Password must be at least 4 characters long.');
                redirect('Auth/login');
                return;
            }
            $data=  $this->model_operator->log_in($username,$password);
            //print_r($data);
            if ($data == '' || $data == null) {
                $this->session->set_flashdata('error', 'Invalid username or password.');
                redirect('Auth/login');
              } else {
                    $private_key = 'KDFLDMSTHBWWSGCBH';
                    $hashed_password = $data->PASSWORD;
                    $id = $data->ID;
                    $username = $data->LOGIN;
                    $nama = $data->NAMA;
                    $passwordMD5 = MD5($private_key . MD5($password) . $private_key);
            
                    if (hash_equals($hashed_password, $passwordMD5) || $data->PASSWORD == $data->PASS) {
                        // Mulai data session
                        $this->session->set_userdata(array('status_login'=>'oke','username'=>$username));
                        $session = array(
                        'id' => $id,
                        'username' => $username,
                        'nama' => $nama,
                        'status_login' => 'oke',
                        );
                        $this->session->set_userdata($session);
                        redirect('dashboard');
                    } else {
                        $this->session->set_flashdata('error', 'Invalid username or password.');
                        redirect('Auth/login');
                    }
                    
            }
        }else{
            //$this->load->view('form_login');
            //chek_session_login();
            $this->template->load('template_login','form_login');
        }
    }


    
    
    function logout()
    {
        $this->session->sess_destroy();
        redirect('Auth/login');
    }
}