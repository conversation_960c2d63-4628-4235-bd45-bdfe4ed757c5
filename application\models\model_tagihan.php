<?php
defined('BASEPATH') or exit('No direct script access allowed');

class model_tagihan extends CI_Model{
    
    public function __construct() {
        parent::__construct();
        // Memuat koneksi database 
        $this->load->database('pembayaran', TRUE);
    }
    

	function simpan($data)
    {
        $this->db->insert('log.ubah_tagihan_ranap', $data);
        return $this->db->insert_id();
    }


    public function getNamaPasien($idtagihan){
        $query="SELECT *, master.getNamaLengkap(pt.REF)  NAMAPASIEN
                    FROM pembayaran.tagihan pt
            WHERE pt.ID = '$idtagihan'";
        $bind = $this->db->query($query);
        return $bind->row_array();
    }
   
    public function get_oneceTagihan($tagihan,$ref,$tarifid){
        $query="	SELECT rincian.*, 
			(SELECT master.getNamaLengkapPegawai(mpdok.NIP)
					FROM layanan.petugas_tindakan_medis ptm 
					     LEFT JOIN master.dokter dok ON ptm.MEDIS=dok.ID
					     LEFT JOIN master.pegawai mpdok ON dok.NIP=mpdok.NIP
					     , pembayaran.rincian_tagihan rt
					WHERE ptm.TINDAKAN_MEDIS=rincian.REF_ID AND rincian.JENIS=3 AND  ptm.JENIS=1 AND ptm.MEDIS!=0 AND ptm.KE=1
						AND ptm.TINDAKAN_MEDIS=rt.REF_ID AND rt.JENIS=3 AND rt.TAGIHAN=rincian.TAGIHAN limit 1) PETUGASMEDIS ,
						0 IDPRABAYAR	
				
		  FROM (/* Administrasi */
	SELECT RAND() QID, rt.TAGIHAN, rt.REF_ID, 
			 CONCAT(
			 	IF(r.JENIS_KUNJUNGAN = 3,
			 		IF(tadm.ADMINISTRASI = 5, '-', CONCAT(r.DESKRIPSI,' (', rk.KAMAR, '/', rkt.TEMPAT_TIDUR, '/', kls.DESKRIPSI, ')')), 
					IF(NOT r1.DESKRIPSI IS NULL, r1.DESKRIPSI, r2.DESKRIPSI))
			 ) RUANGAN,
			 adm.NAMA LAYANAN,
			 rt.JENIS, ref.DESKRIPSI JENIS_RINCIAN,
			 rt.TARIF_ID,
			 IF(rt.JENIS = 1, 
			 	IF(tadm.ADMINISTRASI = 1, krtp.TANGGAL, 
				 	IF(tadm.ADMINISTRASI = 2, kp.TANGGAL, 
					 	IF(tadm.ADMINISTRASI = 3, kj.KELUAR, 
					 		kp.TANGGAL
						)
					 )
				 ), NULL) TANGGAL,
			 rt.JUMLAH, rt.TARIF, rt.`STATUS`
	  FROM pembayaran.rincian_tagihan rt
	  	    /* Kartu */
	  		 LEFT JOIN cetakan.kartu_pasien krtp ON krtp.ID = rt.REF_ID	
	  		 
	  		 /* Karcis RJ or RI */
			 LEFT JOIN cetakan.karcis_pasien kp ON kp.ID = rt.REF_ID AND rt.JENIS = 1
	  		 LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.NOPEN
	  		 LEFT JOIN `master`.tarif_administrasi tadm ON tadm.ID = rt.TARIF_ID 
	  		 LEFT JOIN `master`.administrasi adm ON adm.ID = tadm.ADMINISTRASI
	  		 LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
	  		 LEFT JOIN pendaftaran.reservasi res ON res.NOMOR = tp.RESERVASI
	  		 LEFT JOIN `master`.ruang_kamar_tidur rkt ON rkt.ID = res.RUANG_KAMAR_TIDUR
	  		 LEFT JOIN `master`.ruang_kamar rk ON rk.ID = rkt.RUANG_KAMAR
	  		 LEFT JOIN `master`.ruangan r ON r.ID = rk.RUANGAN
	  		 LEFT JOIN `master`.ruangan r1 ON r1.ID = tp.RUANGAN
	  		 LEFT JOIN `master`.referensi kls ON kls.JENIS = 19 AND kls.ID = rk.KELAS
	  		 LEFT JOIN `master`.referensi ref ON ref.JENIS = 30 AND ref.ID = rt.JENIS
	  		 
	  		 /* Pelayanan Farmasi */
			 LEFT JOIN pendaftaran.kunjungan kj ON kj.NOMOR = rt.REF_ID AND rt.TARIF_ID IN (3,4)
	  		 LEFT JOIN `master`.ruangan r2 ON r2.ID = kj.RUANGAN
	 WHERE rt.TAGIHAN = '$tagihan' AND (r.JENIS_KUNJUNGAN != 3 OR r.JENIS_KUNJUNGAN IS NULL)
	   AND rt.JENIS = 1 #AND tadm.ADMINISTRASI = 2
	UNION
	/* Akomodasi */
	SELECT RAND() QID, rt.TAGIHAN, rt.REF_ID,
			 CONCAT(r.DESKRIPSI,
			 	IF(r.JENIS_KUNJUNGAN = 3,
			 		CONCAT(' (', rk.KAMAR, '/', rkt.TEMPAT_TIDUR, '/', kls.DESKRIPSI, ')'), '')
			 ) RUANGAN,
			 IF(r.JENIS_KUNJUNGAN = 3,
			 		CONCAT(' (', rk.KAMAR, '/', rkt.TEMPAT_TIDUR, '/', kls.DESKRIPSI, ')'), '') LAYANAN,
			 rt.JENIS, ref.DESKRIPSI JENIS_RINCIAN,
			 rt.TARIF_ID,
			 IF(rt.JENIS = 2, kjgn.MASUK, NULL) TANGGAL, 
			 rt.JUMLAH, rt.TARIF, rt.`STATUS`
	  FROM pembayaran.rincian_tagihan rt
	  		 LEFT JOIN pendaftaran.kunjungan kjgn ON kjgn.NOMOR = rt.REF_ID AND rt.JENIS = 2
	  		 LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kjgn.NOPEN
	  		 LEFT JOIN `master`.ruangan r ON r.ID = kjgn.RUANGAN
	  		 LEFT JOIN `master`.ruang_kamar_tidur rkt ON rkt.ID = kjgn.RUANG_KAMAR_TIDUR
	  		 LEFT JOIN `master`.ruang_kamar rk ON rk.ID = rkt.RUANG_KAMAR
	  		 LEFT JOIN `master`.referensi kls ON kls.JENIS = 19 AND kls.ID = rk.KELAS
	  		 LEFT JOIN `master`.referensi ref ON ref.JENIS = 30 AND ref.ID = rt.JENIS
	 WHERE rt.TAGIHAN = '$tagihan'
	   AND rt.JENIS = 2
	UNION
	SELECT RAND() QID, rt.TAGIHAN, rt.REF_ID, 
			 CONCAT(r.DESKRIPSI,
			 	IF(r.JENIS_KUNJUNGAN = 3,
			 		CONCAT(' (', rk.KAMAR, '/', rkt.TEMPAT_TIDUR, '/', kls.DESKRIPSI, ')'), '')
			 ) RUANGAN,
			 ref.DESKRIPSI LAYANAN,
			 rt.JENIS, ref.DESKRIPSI JENIS_RINCIAN,
			 rt.TARIF_ID,
			 IF(rt.JENIS =  6, kjgn.MASUK, NULL) TANGGAL, 
			 rt.JUMLAH, rt.TARIF, rt.`STATUS`
	  FROM pembayaran.rincian_tagihan rt
	  		 LEFT JOIN pendaftaran.kunjungan kjgn ON kjgn.NOMOR = rt.REF_ID
	  		 LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kjgn.NOPEN
	  		 LEFT JOIN `master`.ruangan r ON r.ID = kjgn.RUANGAN
	  		 LEFT JOIN `master`.ruang_kamar_tidur rkt ON rkt.ID = kjgn.RUANG_KAMAR_TIDUR
	  		 LEFT JOIN `master`.ruang_kamar rk ON rk.ID = rkt.RUANG_KAMAR
	  		 LEFT JOIN `master`.referensi kls ON kls.JENIS = 19 AND kls.ID = rk.KELAS
	  		 LEFT JOIN `master`.referensi ref ON ref.JENIS = 30 AND ref.ID = rt.JENIS
	 WHERE rt.TAGIHAN = '$tagihan'
	   AND rt.JENIS = 6
		) rincian 
	   WHERE rincian.STATUS = 1 AND rincian.REF_ID ='$ref' AND rincian.JENIS=2 AND rincian.TARIF_ID='$tarifid'
			ORDER BY TANGGAL
	";
        $bind = $this->db->query($query);
        return $bind->row_array();
    }

	public function listDataLog()
	{
					$hasil="SELECT lut.*, (SELECT  CONCAT(r.DESKRIPSI,
							IF(r.JENIS_KUNJUNGAN = 3,
								CONCAT(' (', rk.KAMAR, '/', rkt.TEMPAT_TIDUR, '/', kls.DESKRIPSI, ')'), '')
						) RUANGAN
				FROM pembayaran.rincian_tagihan rt
						LEFT JOIN pendaftaran.kunjungan kjgn ON kjgn.NOMOR = rt.REF_ID AND rt.JENIS = 2
						LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kjgn.NOPEN
						LEFT JOIN `master`.ruangan r ON r.ID = kjgn.RUANGAN
						LEFT JOIN `master`.ruang_kamar_tidur rkt ON rkt.ID = kjgn.RUANG_KAMAR_TIDUR
						LEFT JOIN `master`.ruang_kamar rk ON rk.ID = rkt.RUANG_KAMAR
						LEFT JOIN `master`.referensi kls ON kls.JENIS = 19 AND kls.ID = rk.KELAS
						LEFT JOIN `master`.referensi ref ON ref.JENIS = 30 AND ref.ID = rt.JENIS
				WHERE rt.TAGIHAN = lut.TAGIHAN
					AND rt.REF_ID = lut.REF_ID
					AND rt.TARIF_ID = lut.TARIF_ID	
				AND rt.JENIS = 2) RUANGAN,
				
				(SELECT  IF(r.JENIS_KUNJUNGAN = 3,
								CONCAT(' (', rk.KAMAR, '/', rkt.TEMPAT_TIDUR, '/', kls.DESKRIPSI, ')'), '') LAYANAN
				FROM pembayaran.rincian_tagihan rt
						LEFT JOIN pendaftaran.kunjungan kjgn ON kjgn.NOMOR = rt.REF_ID AND rt.JENIS = 2
						LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kjgn.NOPEN
						LEFT JOIN `master`.ruangan r ON r.ID = kjgn.RUANGAN
						LEFT JOIN `master`.ruang_kamar_tidur rkt ON rkt.ID = kjgn.RUANG_KAMAR_TIDUR
						LEFT JOIN `master`.ruang_kamar rk ON rk.ID = rkt.RUANG_KAMAR
						LEFT JOIN `master`.referensi kls ON kls.JENIS = 19 AND kls.ID = rk.KELAS
						LEFT JOIN `master`.referensi ref ON ref.JENIS = 30 AND ref.ID = rt.JENIS
				WHERE rt.TAGIHAN = lut.TAGIHAN
					AND rt.REF_ID = lut.REF_ID
					AND rt.TARIF_ID = lut.TARIF_ID	
				AND rt.JENIS = 2) LAYANAN,
				
					(SELECT   ref.DESKRIPSI LAYANAN
				FROM pembayaran.rincian_tagihan rt
						LEFT JOIN pendaftaran.kunjungan kjgn ON kjgn.NOMOR = rt.REF_ID AND rt.JENIS = 2
						LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kjgn.NOPEN
						LEFT JOIN `master`.ruangan r ON r.ID = kjgn.RUANGAN
						LEFT JOIN `master`.ruang_kamar_tidur rkt ON rkt.ID = kjgn.RUANG_KAMAR_TIDUR
						LEFT JOIN `master`.ruang_kamar rk ON rk.ID = rkt.RUANG_KAMAR
						LEFT JOIN `master`.referensi kls ON kls.JENIS = 19 AND kls.ID = rk.KELAS
						LEFT JOIN `master`.referensi ref ON ref.JENIS = 30 AND ref.ID = rt.JENIS
				WHERE rt.TAGIHAN = lut.TAGIHAN
					AND rt.REF_ID = lut.REF_ID
					AND rt.TARIF_ID = lut.TARIF_ID	
				AND rt.JENIS = 2) JENIS_RINCIAN,
				master.getNamaLengkapPegawai(ap.NIP) USERNAMA,
		master.getNamaLengkap(pt.REF) NAMAPASIEN,
		pt.REF NORM
		FROM log.ubah_tagihan_ranap lut
		LEFT JOIN aplikasi.pengguna ap ON ap.ID = lut.OLEH
		LEFT JOIN pembayaran.tagihan pt ON pt.ID = lut.TAGIHAN ORDER BY lut.CREATED DESC  ";
					$bind = $this->db->query($hasil);
			  return $bind;
	}

    public function listData($tagihan,$status)
    {
            $query = $this->db->query("CALL pembayaran.listRincianTagihan('$tagihan','$status')");
            $res = $query->result_array();
            return $res;
    }

    public function listDataf($tagihan,$status){
        $hasil = "CALL pembayaran.listRincianTagihan('$tagihan','$status')";
        $bind = $this->db->query($hasil);
        return $bind;
    }

    public function dataTagihanRanap(){
        $query = $this->db->query("SELECT *, master.getNamaLengkap(pt.REF) NAMAPASIEN FROM pembayaran.tagihan pt
                LEFT JOIN pembayaran.tagihan_pendaftaran ptp ON ptp.TAGIHAN = pt.ID
                LEFT JOIN pendaftaran.tujuan_pasien  pentp ON pentp.NOPEN = ptp.PENDAFTARAN
                LEFT JOIN master.ruangan r ON pentp.RUANGAN = r.ID
                WHERE pt.`STATUS` =1 AND r.JENIS_KUNJUNGAN = 3 AND ptp.`STATUS`=1 AND YEAR(pt.TANGGAL) = YEAR(NOW())");
        $res = $query->result_array();
        return $res;
    }

    public function listDataRanap(){
        $hasil = "SELECT *, master.getNamaLengkap(pt.REF) NAMAPASIEN FROM pembayaran.tagihan pt
                LEFT JOIN pembayaran.tagihan_pendaftaran ptp ON ptp.TAGIHAN = pt.ID
                LEFT JOIN pendaftaran.tujuan_pasien  pentp ON pentp.NOPEN = ptp.PENDAFTARAN
                LEFT JOIN master.ruangan r ON pentp.RUANGAN = r.ID
                WHERE pt.`STATUS` =1 AND r.JENIS_KUNJUNGAN = 3 AND ptp.`STATUS`=1 AND YEAR(pt.TANGGAL) = YEAR(NOW())";
        $bind = $this->db->query($hasil);
        return $bind;
    }

  



}