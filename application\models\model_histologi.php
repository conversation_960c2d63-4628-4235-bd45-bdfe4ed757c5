<?php
defined('BASEPATH') or exit('No direct script access allowed');

class model_histologi extends CI_Model{
    
    public function __construct() {
        parent::__construct();
        // Memuat koneksi database 'layanan'
        $this->load->database('layanan', TRUE);
    }
    
    function edit($data,$id)
    {
        $this->db->where('ID',$id);
        $this->db->update('layanan.hasil_pa_histologi',$data);
    }

    public function get_onece($id){
        $query="SELECT * FROM layanan.hasil_pa_histologi hp
        WHERE hp.ID='$id'";
        $bind = $this->db->query($query);
        return $bind->row_array();
    }

    public function listData()
    {
            $query = $this->db->query("CALL layanan.listDashborHistoUmum()");
            $res = $query->result_array();
            $query->next_result(); 
            $query->free_result(); 
            return $res;
    }

    public function listDataf(){
        $hasil = "CALL layanan.listDashborHistoUmum()";
        $bind = $this->db->query($hasil);
        return $bind;
    }

    public function jenisPemeriksaanBerubah1($nolab){
        $hasil = "CALL layanan.JenisPemeriksaanBerubah('$nolab')";
        $this->db->query($hasil);
        $hasil->next_result(); 
        $hasil->free_result(); 
       // return $res;
    }

    public function jenisPemeriksaanBerubah($nolab) {
        // Prepare the SQL call to the stored procedure
        $hasil = "CALL layanan.JenisPemeriksaanBerubah('$nolab')";
        // Execute the query
        if ($this->db->query($hasil) === TRUE) {
            // If the query is successful, you may want to handle the result
            // For example, if the stored procedure returns a result set
            do {
                // Store the result set
                if ($result = $this->db->store_result()) {
                    // Free the result set
                    $result->free();
                }
            } while ($this->db->more_results() && $this->db->next_result());
        } else {
            // Handle the error if the query fails
            echo "Error: " . $this->db->error;
        }
    }

    public function nokunNoPa($norm){
        $hasil = "CALL layanan.nokunPerubahanNoPa($norm)";
        $bind = $this->db->query($hasil);
        $fix = $bind->result_array();
        $bind->next_result(); 
        $bind->free_result(); 
        return $fix; 
    }

}