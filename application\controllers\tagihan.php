<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class tagihan extends CI_Controller{
    
    function __construct() {
        parent::__construct();
        $this->load->model('model_tagihan');
        $this->load->library('session'); 
        chek_session();
    }


    function index()
    {
        $this->template->load('template','tagihan/lihat_data');
    }
    
    function logranap()
    {
        $this->template->load('template','tagihan/lihat_log');
    }
    
    function edit()
    {
            $idtagihan=  $this->uri->segment(3);
            $data['record']     =    $idtagihan;
            $norm =  $this->uri->segment(4);
            $namapasien = $this->input->get('namapasien');
            $data['pasien'] = $this->model_tagihan->getNamaPasien($idtagihan);
            $data['norm'] = $data['pasien']['REF'];
            $data['namapasien'] = $data['pasien']['NAMAPASIEN'];
            $this->template->load('template','tagihan/detail',$data);
    }

    function ubah()
    {
       if(isset($_POST['submit'])){
            $idtagihan              =   $this->input->post('TAGIHAN');
            $refid                  =   $this->input->post('REF_ID');
            $jenis                  =   $this->input->post('JENIS');
            $tarifid                =   $this->input->post('TARIF_ID'); 
            $jumlah                =   $this->input->post('JUMLAH');
            $jumlahedit               =   $this->input->post('JUMLAH_EDIT');
            $session_id             =   $this->session->userdata('id'); 
            $data = array(
                'TAGIHAN' => $idtagihan,
                'REF_ID' => $refid,
                'JENIS' => $jenis,
                'TARIF_ID' => $tarifid,
                'JUMLAH' => $jumlah,
                'JUMLAH_EDIT' => $jumlahedit,
                'OLEH' => $session_id 
            );          
            $this->model_tagihan->simpan($data);
            redirect('tagihan/edit/'.$idtagihan);
        }else{
            $idtagihan =  $this->uri->segment(3);
            $data['idtagihan'] =    $idtagihan;
            $ref =  $this->uri->segment(4);
            $jenis =  $this->uri->segment(5);
            $idtarif =  $this->uri->segment(6);
            $data['pasien'] = $this->model_tagihan->getNamaPasien($idtagihan);
            $data['record']     =  $this->model_tagihan->get_oneceTagihan($idtagihan,$ref,$idtarif);
            $this->template->load('template','tagihan/form_ubah',$data);
        }
    }

    function test()
    {
        echo waktu_indo();
    }

    function listData(){
        $idtagihan =  $this->uri->segment(3);
        // echo "data : ";
        // echo "<br/>". $idtagihan;
        $data['record'] = $this->model_tagihan->listData($idtagihan, '1');
        print_r($data['record']);
    }

    function listDataTagihanRanap(){
        $idtagihan =  $this->uri->segment(3);
        // echo "data : ";
        // echo "<br/>". $idtagihan;
        $data['record'] = $this->model_tagihan->dataTagihanRanap();
        print_r($data['record']);
    }

    public function getDataTagihanDetail() {
        $draw = intval($this->input->get("draw"));
        $start = intval($this->input->get("start"));
        $length = intval($this->input->get("length"));
        $record = $this->input->get("record"); 
        $query = $this->model_tagihan->listDataf($record,1);
        $data = [];
        $no=0;
        foreach ($query->result() as $row) {
            $no++;
            $editButton = ($row->JENIS == 2) ? '<td width="80"><button class="btn btn-primary" onclick="window.location.href=\''.site_url('tagihan/ubah/'.$row->TAGIHAN.'/'.$row->REF_ID.'/'.$row->JENIS.'/'.$row->TARIF_ID).'\'">Edit Data</button></td>' : '';
            $data[] = [
                $no,
                $row->RUANGAN,
                $row->LAYANAN,
                $row->JENIS_RINCIAN,
                $row->TANGGAL,
                $row->JUMLAH,
                $row->TARIF,
                $editButton, 
            ];
        }
        $result = [
            "draw" => $draw,
            "recordsTotal" =>  $query->num_rows(),
            "recordsFiltered" =>  $query->num_rows(),
            "data" => $data,
        ];
        echo json_encode($result);
        //exit();
    }

    public function getDataTagihan() {
        $draw = intval($this->input->get("draw"));
        $start = intval($this->input->get("start"));
        $length = intval($this->input->get("length"));
        $query = $this->model_tagihan->listDataRanap();
        $data = [];
        $no=0;
        foreach ($query->result() as $row) {
            $no++;
            $viewDetailButton = '<td width="80"><button class="btn btn-primary" onclick="window.location.href=\''.site_url('tagihan/edit/'.$row->TAGIHAN.'/').'\'">View Detail</button></td>';
            $data[] = [
                $no,
                $row->TAGIHAN,
                $row->REF,
                $row->NAMAPASIEN,
                $row->DESKRIPSI,
                $row->TANGGAL,
                $row->TOTAL,
                $viewDetailButton,
               // '<td width="80"><button class="btn btn-primary" onclick="window.location.href=\''.site_url('tagihan/edit/'.$row->TAGIHAN).'?norm='.$row->NORM.'\'">View Detail</button></td>',
            ];
        }
        $result = [
            "draw" => $draw,
            "recordsTotal" =>  $query->num_rows(),
            "recordsFiltered" =>  $query->num_rows(),
            "data" => $data,
        ];
        echo json_encode($result);
        //exit();
    }

    public function getDataLog() {
        $draw = intval($this->input->get("draw"));
        $start = intval($this->input->get("start"));
        $length = intval($this->input->get("length"));
        $query = $this->model_tagihan->listDataLog();
        $data = [];
        $no=0;
        foreach ($query->result() as $row) {
            $no++;
            $data[] = [
                $no,
                $row->NORM,
                $row->NAMAPASIEN,
                $row->RUANGAN,
                $row->LAYANAN,
                $row->JUMLAH,
                $row->JUMLAH_EDIT,
                $row->CREATED,
                $row->USERNAMA,
            ];
        }
        $result = [
            "draw" => $draw,
            "recordsTotal" =>  $query->num_rows(),
            "recordsFiltered" =>  $query->num_rows(),
            "data" => $data,
        ];
        echo json_encode($result);
        //exit();
    }
}