<?php
defined('BASEPATH') OR exit('No direct script access allowed');
class model_operator extends CI_Model{
    
    function __construct(){
		parent::__construct();
        $this->load->database();
	}


    public function log_in($username, $password)
    {
      @$this->db->select(
        'ap.ID, ap.LOGIN, ap.PASSWORD,ap.NAMA, ap.NIP, PASSWORD("' . $password . '") PASS'
      );
      @$this->db->from('aplikasi.pengguna ap');
    //   $this->db->join('akses_simrskd.MENU_USER amu', 'amu.ID_USER = ap.ID', 'left');
    //   $this->db->join('akses_simrskd.MENU am', 'am.ID = amu.ID_MENU', 'left');
      @$this->db->join('master.pegawai pega', 'pega.NIP = ap.NIP', 'left');
    //   $this->db->join('master.dokter d', 'pega.NIP = d.NIP', 'left');
    //   $this->db->join('master.referensi ref', 'ref.ID = pega.SMF AND ref.JENIS=26', 'left');
    //   $this->db->join('master.referensi prof', 'prof.ID = pega.PROFESI AND prof.JENIS = 36', 'left');
    //   $this->db->join('akses_simrskd.USER_RUANGAN ar', 'ap.ID = ar.ID_USER', 'left');
  
      @$this->db->where('ap.LOGIN', $username);
      @$this->db->where('pega.STATUS', 1);
    //   $this->db->group_by('amu.ID_USER');
    //   $this->db->order_by('am.SEQ', 'DESC');
  
      $query = @$this->db->get();
      return @$query->row();
    }
    
    
    function tampildata()
    {
        return $this->db->get('operator');
    }
    
    function get_one($id)
    {
        $param  =   array('operator_id'=>$id);
        return $this->db->get_where('operator',$param);
    }
}